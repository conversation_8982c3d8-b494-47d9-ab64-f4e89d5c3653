use axum::{
    error_handling::HandleErrorLayer,
    extract::State,
    http::StatusCode,
    response::IntoResponse,
    routing::get,
    Json, Router,
};
use serde_json::json;

use std::{
    net::SocketAddr,
    time::Duration,
};
use tower::{BoxError, ServiceBuilder};
use tower_http::trace::TraceLayer;
use tracing_subscriber::EnvFilter;

#[tokio::main]
async fn main() {
    eprintln!("Backend starting...");

    println!("Setting up tracing...");
    tracing_subscriber::fmt()
        .with_env_filter(
            EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "backend=debug,tower_http=debug".into()),
        )
        .init();

    println!("Tracing initialized");

    let state = AppState::default();
    println!("Created app state");

    let app = Router::new()
        .route("/debug", get(debug_handler))
        .route("/health", get(health_handler))
        .layer(
            ServiceBuilder::new()
                .layer(HandleErrorLayer::new(|error: BoxError| async move {
                    if error.is::<tower::timeout::error::Elapsed>() {
                        Ok(StatusCode::REQUEST_TIMEOUT)
                    } else {
                        Err((
                            StatusCode::INTERNAL_SERVER_ERROR,
                            format!("Unhandled internal error: {error}"),
                        ))
                    }
                }))
                .timeout(Duration::from_secs(10))
                .layer(TraceLayer::new_for_http())
                .into_inner(),
        )
        .with_state(state);

    println!("Created router");

    let addr = SocketAddr::from(([0, 0, 0, 0], 8080));
    println!("Attempting to bind to address: {}", addr);
    tracing::debug!("listening on {}", addr);
    
    match tokio::net::TcpListener::bind(addr).await {
        Ok(listener) => {
            println!("Successfully bound to {}", addr);
            println!("Starting server...");
            if let Err(e) = axum::serve(listener, app).await {
                eprintln!("Server error: {}", e);
                std::process::exit(1);
            }
        }
        Err(e) => {
            eprintln!("Failed to bind to {}: {}", addr, e);
            std::process::exit(1);
        }
    }
    
    tracing::info!("backend stopped");
    println!("Backend stopped");
}

#[derive(Clone, Default)]
struct AppState {
    // Add shared app state here if needed later
}

async fn health_handler(State(_state): State<AppState>) -> impl IntoResponse {
    Json(json!({
        "status": "ok",
        "timestamp": chrono::Utc::now().to_rfc3339(),
    }))
}

async fn debug_handler(State(_state): State<AppState>) -> impl IntoResponse {
    Json(json!({
        "status": "ok",
        "version": "0.1.0",
        "uptime": format!("{}", chrono::Utc::now()),
    }))
}