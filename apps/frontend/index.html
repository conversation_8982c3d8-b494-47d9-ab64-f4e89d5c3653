<!DOCTYPE html>
<html>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<!-- Disable zooming: -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

<head>
    <!-- change this to your project name -->
    <title>frontend</title>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- config for our rust wasm binary. go to https://trunkrs.dev/assets/#rust for more customization -->
    <link data-trunk rel="rust" data-wasm-opt="2" />
    <!-- this is the base url relative to which other urls will be constructed. trunk will insert this from the public-url option -->
    <base data-trunk-public-url />

    <link data-trunk rel="icon" href="assets/favicon.ico">


    <link data-trunk rel="copy-file" href="assets/sw.js"/>
    <link data-trunk rel="copy-file" href="assets/manifest.json"/>
    <link data-trunk rel="copy-file" href="assets/icon-1024.png" data-target-path="assets"/>
    <link data-trunk rel="copy-file" href="assets/icon-256.png" data-target-path="assets"/>
    <link data-trunk rel="copy-file" href="assets/icon_ios_touch_192.png" data-target-path="assets"/>
    <link data-trunk rel="copy-file" href="assets/maskable_icon_x512.png" data-target-path="assets"/>


    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="assets/icon_ios_touch_192.png">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#404040">

    <style>
        html {
            /* Remove touch delay: */
            touch-action: manipulation;
        }

        body {
            /* Light mode background color for what is not covered by the egui canvas,
            or where the egui canvas is translucent. */
            background: #FF0000;
        }

        @media (prefers-color-scheme: dark) {
            body {
                /* Dark mode background color for what is not covered by the egui canvas,
                or where the egui canvas is translucent. */
                background: #00FF00;
            }
        }

        /* Allow canvas to fill entire web page: */
        html,
        body {
            overflow: hidden;
            margin: 0 !important;
            padding: 0 !important;
            height: 100%;
            width: 100%;
        }

        /* Make canvas fill entire document: */
        canvas {
            margin-right: auto;
            margin-left: auto;
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .centered {
            margin-right: auto;
            margin-left: auto;
            display: block;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #f0f0f0;
            font-size: 24px;
            font-family: Ubuntu-Light, Helvetica, sans-serif;
            text-align: center;
        }

        /* ---------------------------------------------- */
        /* Loading animation from https://loading.io/css/ */
        .lds-dual-ring {
            display: inline-block;
            width: 24px;
            height: 24px;
        }

        .lds-dual-ring:after {
            content: " ";
            display: block;
            width: 24px;
            height: 24px;
            margin: 0px;
            border-radius: 50%;
            border: 3px solid #fff;
            border-color: #fff transparent #fff transparent;
            animation: lds-dual-ring 1.2s linear infinite;
        }

        @keyframes lds-dual-ring {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Login Screen Styles */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .login-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 400px;
            width: 90%;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            color: #333;
            font-weight: 300;
        }

        .login-header p {
            margin: 0 0 30px 0;
            color: #666;
            font-size: 1.1em;
        }

        .login-content p {
            margin: 0 0 30px 0;
            color: #555;
            font-size: 1em;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-icon {
            font-size: 1.2em;
        }

        .login-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
            min-height: 20px;
        }

        .login-status.error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .login-status.success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }

        .login-status.info {
            background: #eef;
            color: #336;
            border: 1px solid #ccf;
        }

        /* Hide login screen when authenticated */
        .authenticated .login-container {
            display: none;
        }

        .authenticated #the_canvas_id {
            display: block !important;
        }
    </style>
</head>

<body>
    <!-- Login Screen -->
    <div id="login_screen" class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>🗺️ Warda</h1>
                <p>Welcome to the Warda Platform</p>
            </div>
            <div class="login-content">
                <p>Please sign in to continue</p>
                <button id="login_button" class="login-btn">
                    <span class="login-icon">🔐</span>
                    Sign In with Keycloak
                </button>
                <div id="login_status" class="login-status"></div>
            </div>
        </div>
    </div>

    <!-- The WASM code will resize the canvas dynamically -->
    <!-- the id is hardcoded in main.rs . so, make sure both match. -->
    <canvas id="the_canvas_id" style="display: none;"></canvas>

    <!-- the loading spinner will be removed in main.rs -->
    <div class="centered" id="loading_text" style="display: none;">
        <p style="font-size:16px">
            Loading application…
        </p>
        <div class="lds-dual-ring"></div>
    </div>

    <!-- Environment Configuration -->
    <script>
        // Set configuration from environment variables (injected by container)
        // Auto-detect environment and use appropriate Keycloak URL
        function getKeycloakUrl() {
            // If environment variable is set, use it
            if ('${KEYCLOAK_URL}' !== '$' + '{KEYCLOAK_URL}') {
                return '${KEYCLOAK_URL}';
            }

            // Auto-detect based on current URL
            const hostname = window.location.hostname;
            const port = window.location.port;

            console.log('Detecting environment - hostname:', hostname, 'port:', port);

            // For localhost or minikube service URLs, use port forwarding
            if (hostname === 'localhost' ||
                hostname === '127.0.0.1' ||
                port === '3000' ||
                hostname.includes('192.168') ||
                hostname.includes('minikube')) {
                console.log('Local development detected, using port forwarding');
                return 'http://localhost:8080';
            }

            // For .local domains, use ingress approach
            if (hostname.endsWith('.local')) {
                console.log('Local domain detected, using ingress approach');
                return 'http://keycloak.local';
            }

            // Cloud environments
            if (hostname.includes('staging')) {
                return 'https://keycloak-staging.yourdomain.com';
            }

            if (hostname.includes('prod') || !hostname.includes('dev')) {
                return 'https://auth.yourdomain.com';
            }

            // Default fallback for local development
            console.log('Using fallback port forwarding');
            return 'http://localhost:8080';
        }

        window.KEYCLOAK_URL = getKeycloakUrl();
        window.KEYCLOAK_REALM = '${KEYCLOAK_REALM}' !== '$' + '{KEYCLOAK_REALM}' ? '${KEYCLOAK_REALM}' : 'warda';
        window.KEYCLOAK_CLIENT_ID = '${KEYCLOAK_CLIENT_ID}' !== '$' + '{KEYCLOAK_CLIENT_ID}' ? '${KEYCLOAK_CLIENT_ID}' : 'warda-frontend';

        console.log('Keycloak configuration:', {
            url: window.KEYCLOAK_URL,
            realm: window.KEYCLOAK_REALM,
            clientId: window.KEYCLOAK_CLIENT_ID
        });
    </script>

    <!-- Authentication and App Initialization -->
    <script>
        // Global variables for authentication state
        window.keycloakAuth = null;
        window.authToken = null;
        window.userInfo = null;
        window.keycloak = null;

        // Authentication initialization
        async function initAuth() {
            try {
                // Initialize Keycloak instance
                const KeycloakConstructor = typeof Keycloak !== 'undefined' ? Keycloak : window.Keycloak;
                if (typeof KeycloakConstructor === 'undefined') {
                    throw new Error('Keycloak constructor not available');
                }

                window.keycloak = new KeycloakConstructor({
                    url: window.KEYCLOAK_URL,
                    realm: window.KEYCLOAK_REALM,
                    clientId: window.KEYCLOAK_CLIENT_ID
                });

                const authenticated = await window.keycloak.init({
                    onLoad: 'login-required',
                    checkLoginIframe: false,
                    pkceMethod: 'S256',
                    redirectUri: window.location.origin + '/'
                });

                if (authenticated) {
                    await handleAuthenticatedUser();
                } else {
                    showLoginScreen();
                }
            } catch (error) {
                console.error('Authentication failed:', error);
                showLoginScreen();
            }
        }

        // Handle authenticated user
        async function handleAuthenticatedUser() {
            // Store authentication state
            window.keycloakAuth = window.keycloak;
            window.authToken = window.keycloak.token;

            // Load user info
            try {
                window.userInfo = await window.keycloak.loadUserInfo();
            } catch (e) {
                console.warn('Failed to load user info:', e);
            }

            // Set up token refresh
            setInterval(() => {
                window.keycloak.updateToken(30).then((refreshed) => {
                    if (refreshed) window.authToken = window.keycloak.token;
                }).catch(() => console.log('Token refresh failed'));
            }, 60000);

            // Show app
            hideLoginScreen();
            authenticationComplete();
            // Call update_auth_state in WASM (if exported)
            if (window.wasm_bindgen && window.wasm_bindgen.update_auth_state) {
                window.wasm_bindgen.update_auth_state();
            }
        }

        // Show login screen
        function showLoginScreen() {
            // Show/hide UI elements
            const loginScreen = document.getElementById('login_screen');
            const canvas = document.getElementById('the_canvas_id');
            const loadingText = document.getElementById('loading_text');

            if (loginScreen) loginScreen.style.display = 'flex';
            if (canvas) canvas.style.display = 'none';
            if (loadingText) loadingText.style.display = 'none';

            const loginButton = document.getElementById('login_button');
            const loginStatus = document.getElementById('login_status');

            if (!loginButton || !loginStatus) return;

            // Set up login functionality
            if (window.keycloak) {
                loginButton.disabled = false;
                loginStatus.textContent = 'Ready to login';
                loginStatus.className = 'login-status info';

                loginButton.onclick = function() {
                    try {
                        loginButton.disabled = true;
                        loginStatus.textContent = 'Redirecting to login...';
                        loginStatus.className = 'login-status info';
                        window.keycloak.login();
                    } catch (error) {
                        console.error('Login failed:', error);
                        loginStatus.textContent = 'Login failed. Please try again.';
                        loginStatus.className = 'login-status error';
                        loginButton.disabled = false;
                    }
                };
            } else {
                loginButton.disabled = true;
                loginStatus.textContent = 'Initializing authentication...';
                loginStatus.className = 'login-status info';
                setTimeout(() => showLoginScreen(), 1000);
            }
        }

        // Hide login screen and show the app
        function hideLoginScreen() {
            document.getElementById('login_screen').style.display = 'none';
            document.getElementById('the_canvas_id').style.display = 'block';
            document.body.classList.add('authenticated');
        }

        // Function to signal that authentication is complete
        function authenticationComplete() {
            console.log('Authentication complete, WASM app can proceed');
            // This function can be called by the WASM app to know when auth is ready
            window.authenticationReady = true;

            // Trigger any waiting WASM initialization
            if (window.wasmInitCallback) {
                window.wasmInitCallback();
            }
        }

        // Expose a JS function to get the current Keycloak token for WASM
        window.getAuthToken = function() {
            return window.keycloak && window.keycloak.token ? window.keycloak.token : null;
        }
    </script>

    <!-- Keycloak JavaScript Adapter -->
    <script>
        // Load Keycloak adapter
        const script = document.createElement('script');
        script.src = window.KEYCLOAK_URL + '/js/keycloak.js';
        script.onload = function() {
            // Wait for Keycloak constructor to be available
            const checkKeycloak = () => {
                if (typeof Keycloak !== 'undefined' || typeof window.Keycloak !== 'undefined') {
                    initAuth();
                } else {
                    setTimeout(checkKeycloak, 100);
                }
            };
            setTimeout(checkKeycloak, 50);
        };
        script.onerror = function() {
            console.error('Failed to load Keycloak adapter from:', window.KEYCLOAK_URL);
            console.error('Keycloak URL was:', window.KEYCLOAK_URL);
            console.error('Failed to load authentication. Please check Keycloak configuration.');

            // Show login screen anyway with error message
            showLoginScreen();
        };
        document.head.appendChild(script);
    </script>



    </script>

    <!--Register Service Worker. this will cache the wasm / js scripts for offline use (for PWA functionality). -->
    <!-- Force refresh (Ctrl + F5) to load the latest files instead of cached files  -->
    <script>
        // We disable caching during development so that we always view the latest version.
        if ('serviceWorker' in navigator && window.location.hash !== "#dev") {
            window.addEventListener('load', function () {
                navigator.serviceWorker.register('sw.js');
            });
        }
    </script>
</body>

</html>
